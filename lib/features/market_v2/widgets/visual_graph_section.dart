import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/widgets/common_shimmer.dart';
import 'package:sf_app/core/widgets/shared_error.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app/features/market_v2/widgets/visual_graph_card.dart';

import '../../../flavors.dart';
import 'visual_graph_card_sis.dart';

class VisualGraphSection extends StatelessWidget {
  final bool isHome;
  const VisualGraphSection({super.key, this.isHome = false});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, StockResponse?)>(
      selector: (state) => (state.stockFetchStatus, state.stockResponse),
      builder: (context, state) {
        final list = state.$2?.data?.list;

        if (state.$1 == DataStatus.loading && list == null) {
          return _buildLoading();
        }

        if (state.$1 == DataStatus.failed || list == null) {
          return const SharedError();
        }

        if (isHome) {
          return _buildHomeSection(list);
        }

        return IntrinsicHeight(
          child: Row(
            children: list
                .map(
                  (stock) => Expanded(
                    child: switch (F.appFlavor) {
                      Flavor.sis => VisualGraphCardSis(
                          data: stock,
                        ),
                      _ => VisualGraphCard(
                          data: stock,
                        ),
                    },
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildHomeSection(List<StockItem> list) {
    return Container(
      height: 180,
      padding: const EdgeInsets.only(right: 12),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: list.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final stock = list[index];
          return switch (F.appFlavor) {
            Flavor.sis => VisualGraphCardSis(
                data: stock,
              ),
            _ => VisualGraphCard(
                data: stock,
              ),
          };
        },
      ),
    );
  }

  Widget _buildLoading() {
    return Row(
      children: List.generate(
        3,
        (index) => Expanded(
                      child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 3),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: const CommonShimmer(
                  height: 180,
                  width: 30,
                ),
              ),
            ),
        ),
      ),
    );
  }
}
