import 'package:auto_size_text/auto_size_text.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/extention.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/utils/utils.dart';
import 'package:sf_app/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app/features/market_v2/kline_detail_screen.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app/features/market_v2/utils/utils.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

const limit = 112;

class VisualGraphCardSis extends StatefulWidget {
  final StockItem? data;

  const VisualGraphCardSis({
    required this.data,
    super.key,
  });

  @override
   State<VisualGraphCardSis> createState() => _VisualGraphCardSisState();
}

class _VisualGraphCardSisState extends State<VisualGraphCardSis>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    if (widget.data == null) return const SizedBox.shrink();

    final name =
        stockIndexTranslation[widget.data!.name] ?? widget.data!.name ?? "";
    final price = (widget.data!.latestPrice ?? 0.0).toStringAsFixed(2);
    final change = (widget.data!.chg ?? 0.0).toStringAsFixed(2);
    final percentageChange =
        ((widget.data!.gain ?? 0.0 * 100) * 100).toStringAsFixed(2);

    final doublePrice = double.tryParse(price) ?? 0.0;

    final instrument = getInstrumentId(widget.data);

    final textColor = (widget.data!.chg ?? 0.0).getValueColor(context);
    final lineColor = (widget.data!.chg ?? 0.0).getValueColor(context);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (ctx) => BlocProvider.value(
              value: context.read<MarketCubit>(),
              child: KlineDetailScreen(
                stock: widget.data,
                disableQuotes: true,
              ),
            ),
          ),
        );
      },
      child: Container(
        height: 160,
        width: 130,
        padding: const EdgeInsets.only( top: 12, bottom:12),
        margin: const EdgeInsets.symmetric(horizontal: 3),
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor4,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 2,
              spreadRadius: 0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 16,
                    child: AutoSizeText(
                      name,
                      style: FontPalette.bold12
                          .copyWith(color: myColorScheme(context).titleColor),
                      maxLines: 1,
                      minFontSize: 8,
                      maxFontSize: 12,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                  const SizedBox(height: 4),
                  SizedBox(
                    height: 20,
                    child: AutoSizeText(
                      doublePrice.toString().toCurrency(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                      maxLines: 2,
                      minFontSize: 10,
                      maxFontSize: 14,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                  const SizedBox(height: 4),
                  SizedBox(
                    height: 16,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AutoSizeText(
                          '${(widget.data!.chg ?? 0.0) > 0 ? "+" : ""}$change',
                          style: TextStyle(
                            fontSize: 10,
                            color: textColor,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 2,
                          minFontSize: 6,
                          maxFontSize: 10,
                          overflow: TextOverflow.visible,
                          textAlign: TextAlign.left,
                        ),
                        const SizedBox(width: 2),
                        AutoSizeText(
                          '($percentageChange%)',
                          style: TextStyle(
                            fontSize: 10,
                            color: textColor,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          minFontSize: 6,
                          maxFontSize: 10,
                          overflow: TextOverflow.visible,
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: BlocSelector<MarketCubit, MarketState, StockKlineData?>(
                selector: (state) => checkAndGetKlineItem(
                  instrument: instrument,
                  coreKlineList: state.miniKlineList,
                ),
                builder: (context, klineData) {
                  final spots = _getSpots(klineData);
                  return Shimmer(
                    enabled: spots.isEmpty,
                    color: ColorPalette.shimmerColor,
                    child: LineChart(
                      duration: const Duration(milliseconds: 0),
                      LineChartData(
                        lineTouchData: const LineTouchData(enabled: false),
                        gridData: const FlGridData(show: false),
                        titlesData: const FlTitlesData(show: false),
                        borderData: FlBorderData(
                          show: false,
                          border: Border.all(
                            color: textColor.withAlpha(30),
                          ),
                        ),
                        minX: 0,
                        minY: 0,
                        maxX: double.tryParse('${spots.length}'),
                        lineBarsData: [
                          LineChartBarData(
                            spots: spots,
                            isCurved: true,
                            color: lineColor,
                            barWidth: 0.3,
                            isStrokeCapRound: true,
                            dotData: FlDotData(
                              show: true,
                              getDotPainter: (spot, percent, bar, index) {
                                if (index == spots.length - 1) {
                                  return FlDotCirclePainter(
                                    radius: 2,
                                    color: textColor,
                                  );
                                }
                                return FlDotCirclePainter(radius: 0);
                              },
                            ),
                            belowBarData: BarAreaData(
                              show: true,
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  lineColor.withValues(alpha: 0.3),
                                  Colors.white.withValues(alpha: 0.3),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

List<FlSpot> _getSpots(StockKlineData? klineData) {
  if (klineData?.list == null || klineData!.list!.isEmpty) return [];

  final list = klineData.list!;

  try {
    final klinePrices = list.map((item) {
      if (item.price == 0.0) {
        return (item.open ?? 0.0);
      } else {
        return (item.price ?? 0.0);
      }
    }).toList();

    final paddedPrices = klinePrices.length >= limit
        ? klinePrices
        : List.generate(limit, (index) {
            if (index < klinePrices.length) {
              return klinePrices[index];
            } else {
              return null;
            }
          });

    final validPrices =
        paddedPrices.where((price) => price != null).cast<double>();
    final minValue =
        validPrices.isEmpty ? 0 : validPrices.reduce((a, b) => a < b ? a : b);
    final maxValue =
        validPrices.isEmpty ? 0 : validPrices.reduce((a, b) => a > b ? a : b);

    final normalizedPrices = paddedPrices.map((price) {
      if (price == null) return null;
      return maxValue - minValue == 0
          ? 0
          : (price - minValue) / (maxValue - minValue) * 10;
    }).toList();

    final spots = normalizedPrices.asMap().entries.map((entry) {
      if (entry.value == null) return FlSpot.nullSpot;
      return FlSpot(entry.key.toDouble(), entry.value!.toDouble());
    }).toList();

    return spots;
  } catch (e) {
    return [];
  }
}
