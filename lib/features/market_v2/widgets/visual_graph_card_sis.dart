import 'package:auto_size_text/auto_size_text.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/utils/utils.dart';
import 'package:sf_app/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app/features/market_v2/kline_detail_screen.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app/features/market_v2/utils/utils.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

const limit = 112;

class VisualGraphCardSis extends StatefulWidget {
  final StockItem? data;

  const VisualGraphCardSis({
    required this.data,
    super.key,
  });

  @override
   State<VisualGraphCardSis> createState() => _VisualGraphCardSisState();
}

class _VisualGraphCardSisState extends State<VisualGraphCardSis>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    if (widget.data == null) return const SizedBox.shrink();

    final name =
        stockIndexTranslation[widget.data!.name] ?? widget.data!.name ?? "";
    final price = (widget.data!.latestPrice ?? 0.0).toStringAsFixed(2);
    final percentageChange =
        ((widget.data!.gain ?? 0.0 * 100) * 100).toStringAsFixed(2);

    final doublePrice = double.tryParse(price) ?? 0.0;

    final instrument = getInstrumentId(widget.data);

    final textColor = (widget.data!.chg ?? 0.0).getValueColor(context);
    final lineColor = (widget.data!.chg ?? 0.0).getValueColor(context);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (ctx) => BlocProvider.value(
              value: context.read<MarketCubit>(),
              child: KlineDetailScreen(
                stock: widget.data,
                disableQuotes: true,
              ),
            ),
          ),
        );
      },
      child: Container(
        height: 180,
        width: 160,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor4,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company Name
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 4),
              child: AutoSizeText(
                name,
                style: FontPalette.bold12
                    .copyWith(color: myColorScheme(context).titleColor),
                maxLines: 1,
                minFontSize: 10,
                maxFontSize: 14,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Price and Percentage Row
            Row(
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 12),
                  width: 2,
                  height: 40,
                  decoration: BoxDecoration(
                    color: textColor,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: AutoSizeText(
                                '\$${doublePrice.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: myColorScheme(context).titleColor,
                                ),
                                maxLines: 1,
                                minFontSize: 14,
                                maxFontSize: 18,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                              decoration: BoxDecoration(
                                color: textColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    (widget.data!.chg ?? 0.0) >= 0
                                        ? Icons.arrow_drop_up_outlined
                                        : Icons.arrow_drop_down_outlined,
                                    color: Colors.white,
                                    size: 14,
                                  ),
                                  Text(
                                    '$percentageChange%',
                                    style: const TextStyle(
                                      fontSize: 7,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Secondary Price
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 2, 12, 8),
                        child: AutoSizeText(
                          '\$${(doublePrice * 0.95).toStringAsFixed(2)}', // Example secondary price
                          style: TextStyle(
                            fontSize: 12,
                            color: myColorScheme(context).titleColor?.withValues(alpha: 0.6) ?? Colors.grey,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          minFontSize: 10,
                          maxFontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: BlocSelector<MarketCubit, MarketState, StockKlineData?>(
                selector: (state) => checkAndGetKlineItem(
                  instrument: instrument,
                  coreKlineList: state.miniKlineList,
                ),
                builder: (context, klineData) {
                  final spots = _getSpots(klineData);

                  if (spots.isEmpty) {
                    // Show shimmer when loading
                    return Shimmer(
                      enabled: true,
                      color: ColorPalette.shimmerColor,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    );
                  }

                  // Show chart with dashed line when data is loaded
                  return Stack(
                    children: [
                      // Horizontal dashed line - only show when graph is loaded
                      Positioned(
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: CustomPaint(
                          painter: DashedLinePainter(
                            color: lineColor.withValues(alpha: 0.4), // Match line color
                          ),
                        ),
                      ),
                      // Line chart
                      LineChart(
                        duration: const Duration(milliseconds: 0),
                        LineChartData(
                          lineTouchData: const LineTouchData(enabled: false),
                          gridData: const FlGridData(show: false),
                          titlesData: const FlTitlesData(show: false),
                          borderData: FlBorderData(show: false),
                          minX: 0,
                          minY: 0,
                          maxX: double.tryParse('${spots.length}'),
                          lineBarsData: [
                            LineChartBarData(
                              spots: spots,
                              isCurved: true,
                              color: lineColor,
                              barWidth: 1.5,
                              isStrokeCapRound: true,
                              dotData: const FlDotData(show: false),
                              belowBarData: BarAreaData(
                                show: true,
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    lineColor.withValues(alpha: 0.2),
                                    lineColor.withValues(alpha: 0.05),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

List<FlSpot> _getSpots(StockKlineData? klineData) {
  if (klineData?.list == null || klineData!.list!.isEmpty) return [];

  final list = klineData.list!;

  try {
    final klinePrices = list.map((item) {
      if (item.price == 0.0) {
        return (item.open ?? 0.0);
      } else {
        return (item.price ?? 0.0);
      }
    }).toList();

    final paddedPrices = klinePrices.length >= limit
        ? klinePrices
        : List.generate(limit, (index) {
            if (index < klinePrices.length) {
              return klinePrices[index];
            } else {
              return null;
            }
          });

    final validPrices =
        paddedPrices.where((price) => price != null).cast<double>();
    final minValue =
        validPrices.isEmpty ? 0 : validPrices.reduce((a, b) => a < b ? a : b);
    final maxValue =
        validPrices.isEmpty ? 0 : validPrices.reduce((a, b) => a > b ? a : b);

    final normalizedPrices = paddedPrices.map((price) {
      if (price == null) return null;
      return maxValue - minValue == 0
          ? 0
          : (price - minValue) / (maxValue - minValue) * 10;
    }).toList();

    final spots = normalizedPrices.asMap().entries.map((entry) {
      if (entry.value == null) return FlSpot.nullSpot;
      return FlSpot(entry.key.toDouble(), entry.value!.toDouble());
    }).toList();

    return spots;
  } catch (e) {
    return [];
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.dashWidth = 4.0,
    this.dashSpace = 4.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final y = size.height / 2;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
